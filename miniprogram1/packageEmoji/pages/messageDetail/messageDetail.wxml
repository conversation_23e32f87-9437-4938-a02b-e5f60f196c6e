<view class="gradient-background3">
  <!-- 导航栏设置 -->
  <van-nav-bar class="navsetting" fixed="true" placeholder="true" bind:click-left="onClickLeft">
    <text class="navtext" slot="right">北航</text>
    <view slot="left">
      <image src="/images/chexiao.png" style="width: 60rpx; height: 55rpx; margin-top: 30rpx;" />
    </view>
  </van-nav-bar>
  <view wx:if="{{showModal}}" class="modal-mask" catchtouchmove="true"></view>

  <!-- 帖子显示 - 匿名和普通都使用相同结构 -->
  <view class="num-item2" style="background-color: rgba({{abc}}); height: auto;">
    <view class="touxiang1" style="display: flex; justify-content: space-between; align-items: center;">
      <view style="display: flex; align-items: center;">
        <image src="{{message.face_url}}" style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
        <view wx:if="{{!message.weizhi}}">
          <view style="display: flex; align-items: center;">
            <text style="margin-left:15rpx;">{{message.is_anonymous_display ? message.anonymous_name : message.username}}</text>
            <view wx:if="{{message.is_anonymous_display}}" class="cyber-badge">
              <view class="cyber-btn">
                帖主
                <view class="cyber-number">OP</view>
              </view>
            </view>
            <text wx:elif="{{message.titlename && message.titlename !== '无' && message.titlename !== 'undefined'}}" class="touxian2 bg{{message.titlecolor}}">{{message.titlename}}</text>
          </view>
        </view>

        <!-- 如果 message.weizhi 不为空，显示这段代码 -->
        <view wx:if="{{message.weizhi}}">
          <view style="margin-bottom: -25rpx;display: flex; align-items: center;">
            <text style="margin-left:15rpx;">{{message.is_anonymous_display ? message.anonymous_name : message.username}}</text>
            <view wx:if="{{message.is_anonymous_display}}" class="cyber-badge">
              <view class="cyber-btn">
                帖主
                <view class="cyber-number">OP</view>
              </view>
            </view>
            <text wx:elif="{{message.titlename && message.titlename !== '无' && message.titlename !== 'undefined'}}" class="touxian2 bg{{message.titlecolor}}">{{message.titlename}}</text>
          </view>
          <view>
            <text style="margin-left:15rpx; font-size: 24rpx;color: #808080;">{{message.weizhi}}</text>
          </view>
        </view>
      </view>
      <!-- 右上角管理按钮 -->
      <view class="manage-btn" bindtap="showManageOptions">
        <image src="/images/gengduo.png" style="width: 40rpx; height: 40rpx;"></image>
      </view>
    </view>
    <rich-text user-select='true' style="letter-spacing: 0rpx; line-height: 45rpx; overflow: hidden; margin-top: 10rpx;" class="text">{{message.content}}</rich-text>
    <view wx:if="{{message.images && message.images.length > 0}}">
      <view class="image-container">
        <image wx:for="{{message.images}}"
               wx:key="index"
               src="{{item}}"
               mode="aspectFill"
               class="uniform-image"
               bindtap="handleImageClick"
               data-url="{{item}}"
               data-index="{{index}}">
        </image>
      </view>
    </view>

    <!-- 投票区域 -->
    <view wx:if="{{message.has_vote && message.vote}}" class="vote-container">
      <view class="vote-header">
        <view class="vote-title-row">
          <image src="/images/xingxingx.png" class="vote-title-icon"></image>
          <text class="vote-title">{{message.vote.title}}</text>
          <view class="vote-type-tag">{{message.vote.vote_type === 'single' ? '单选' : '多选'}}</view>
        </view>
      </view>

      <view class="vote-options">
        <block wx:for="{{message.vote.options}}" wx:key="index">
          <!-- 已投票状态下的选项显示 -->
          <view wx:if="{{message.vote.has_voted || message.vote.is_expired}}"
                class="vote-option-box {{message.vote.user_choices[index] ? 'voted' : ''}}">
            <view class="vote-option-content">
              <text class="vote-option-text">{{item.text}}</text>
              <text class="vote-count-text">{{message.vote.statistics.options_count[index] || 0}}</text>
            </view>
            <view class="vote-progress-bg">
              <view class="vote-progress-fill" style="width: {{item.progressWidth}}%"></view>
            </view>
          </view>

          <!-- 未投票状态下的选项显示 -->
          <view wx:else
                class="vote-option-box {{voteSelection[index] ? 'selected' : ''}}"
                bindtap="handleVoteSelect"
                data-index="{{index}}">
            <view class="vote-option-content">
              <text class="vote-option-text">{{item.text}}</text>
            </view>
            <view class="vote-progress-bg">
              <view class="vote-progress-fill" style="width: {{voteSelection[index] ? '100%' : '0'}}"></view>
            </view>
          </view>
        </block>

        <!-- 投票按钮区域 -->
        <view wx:if="{{!message.vote.has_voted && !message.vote.is_expired}}"
              class="vote-button-area {{message.vote.vote_type === 'multiple' && hasVoteSelection ? 'show-multi' : 'show-single'}}">
          <button class="vote-submit-button {{hasVoteSelection ? 'active' : ''}}"
                  bindtap="submitVote"
                  disabled="{{!hasVoteSelection}}">提交投票</button>
        </view>
      </view>
    </view>

    <view class="kuang" style="display: flex; justify-content: space-between; align-items: center;">
      <view class="gradient-text" style="line-height: 35rpx;">{{message.send_timestamp}}</view>
      <view class="last" style="font-size: 25rpx; display: flex; align-items: center; margin-left: auto;">
        <!-- 有jine时，jine在联系按钮左边 -->
        <block wx:if="{{message.vx || message.qq || message.phonenumber}}">
          <view wx:if="{{message.jine}}" style="color: rgb(226, 114, 23); margin-right: 15rpx;">{{message.jine}}</view>
          <view class="contact-btn" bindtap="dianji">
            <text class="contact-text">联系</text>
            <image src="/images/fenxiang2.png" class="action-icon" />
          </view>
        </block>
        <!-- 无联系方式时，jine在右下角 -->
        <view wx:else style="color: rgb(226, 114, 23);">{{message.jine}}</view>
      </view>
    </view>
  </view>

  <!-- 分割线 -->
  <view class="divider"></view>

  <!-- 评论功能栏 -->
  <view class="comment-function-bar">
    <view class="left-section {{guideStep === 1 && showGuideHint ? 'guide-active' : ''}}">
      <view class="flip-text {{isFlipped ? 'flipped' : ''}}" bindtap="handleFlip">
        <view class="flip-inner">
          <view class="flip-front">评论区</view>
          <view class="flip-back">{{message.views}}°C</view>
        </view>
      </view>
    </view>
    <view class="right-section {{guideStep === 2 && showGuideHint ? 'guide-active' : ''}} {{sortType === 'time' ? 'time-active' : ''}}">
      <button class="sort-btn {{sortType === 'time' ? 'active' : ''}}" hover-class="none" bindtap="changeSort" data-type="time">
        <text>时间</text>
        <text wx:if="{{sortType === 'time'}}" class="sort-arrow {{timeOrder === 'asc' ? 'arrow-up' : 'arrow-down'}}">↓</text>
      </button>
      <button class="sort-btn {{sortType === 'likes' ? 'active' : ''}}" hover-class="none" bindtap="changeSort" data-type="likes">
        <text>赞数</text>
        <text wx:if="{{sortType === 'likes'}}" class="sort-arrow {{likesOrder === 'asc' ? 'arrow-up' : 'arrow-down'}}">↓</text>
      </button>
    </view>
  </view>

  <!-- 群通知组件 -->
  <group-notice />

  <!-- 评论列表 -->
  <view wx:if="{{comment && comment.length > 0}}" wx:for="{{comment}}" wx:for-item="item" wx:for-index="index" wx:key="index"
        class="num-item2 {{item.id === highlightedCommentId ? 'highlighted' : ''}} {{item.id === longPressCommentId ? 'active-longpress' : ''}}"
        style="height: auto;"
        id="comment-{{item.id}}"
        bindlongpress="showCommentActionSheet"
        data-comment="{{item}}">

    <!-- 评论显示 - 匿名和普通都使用相同结构 -->
    <view>
      <view class="comment-content" bindtap="onReplyClick"
            data-id="{{item.id}}"
            data-username="{{message.is_anonymous_display ? item.anonymous_name : item.username}}"
            data-type="comment"
            data-userid="{{message.is_anonymous_display ? item.original_user_id : item.user_id}}"
            style="-webkit-tap-highlight-color: transparent !important;">
        <view class="touxiang1">
          <image src="{{item.face_url}}" style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
          <text style="margin-left:15rpx;">{{message.is_anonymous_display ? item.anonymous_name : item.username}}</text>
          <view wx:if="{{message.is_anonymous_display && item.is_original_poster}}" class="cyber-badge">
            <view class="cyber-btn">
              帖主
              <view class="cyber-number">OP</view>
            </view>
          </view>
          <text wx:elif="{{!message.is_anonymous_display && item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
        </view>
        <view style="line-height: 45rpx; overflow: hidden; word-wrap: break-word; margin-top:15rpx;" class="text">{{item.content}}</view>
        <!-- 添加评论图片显示 -->
        <view wx:if="{{item.images && item.images.length > 0}}" class="image-container" catch:tap="onReplyClick" data-id="{{item.id}}" data-username="{{message.is_anonymous_display ? item.anonymous_name : item.username}}" data-type="comment" data-userid="{{message.is_anonymous_display ? item.original_user_id : item.user_id}}">
          <image wx:for="{{item.images}}" wx:key="index" wx:for-item="img"
                 src="{{img}}" mode="aspectFill" class="uniform-image"
                 catchtap="handleCommentImageClick"
                 data-url="{{img}}"
                 data-images="{{item.images}}">
          </image>
        </view>
      </view>
      <!-- 评论内容下方的时间和点赞区域 -->
      <view class="kuang" style="display: flex; justify-content: space-between; align-items: center; height: 50rpx; -webkit-tap-highlight-color: transparent !important;">
        <view class="gradient-text" style="line-height: 50rpx; flex: 1; -webkit-tap-highlight-color: transparent !important;" bindtap="onReplyClick" data-id="{{item.id}}" data-username="{{message.is_anonymous_display ? item.anonymous_name : item.username}}" data-type="comment" data-userid="{{message.is_anonymous_display ? item.original_user_id : item.user_id}}">{{item.send_timestamp}}</view>
        <view class="last" style="font-size: 25rpx; display: flex; align-items: center; -webkit-tap-highlight-color: transparent !important;" catchtap="dolike2" data-id="{{item.id}}">
          <image src="{{item.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" mode="aspectFit" style="width: 35rpx; height: 35rpx; -webkit-tap-highlight-color: transparent !important;" />
          <view style="margin-left: 10rpx; -webkit-tap-highlight-color: transparent !important;">{{item.total_likes}}</view>
        </view>
      </view>
    </view>

    <!-- 评论的回复显示 -->
    <view wx:if="{{item.replies.length > 0}}" class="replies-container" style="padding-left: 20rpx; margin-top: 10rpx;">
      <view wx:for="{{item.replies}}"
            wx:key="reply_index"
            wx:for-item="reply"
            wx:for-index="reply_index"
            wx:if="{{reply_index < (expandedComments[item.id] || defaultRepliesShow)}}"
            class="reply-item {{reply.id === highlightedReplyId ? 'highlight-item' : ''}} {{reply.id === longPressReplyId ? 'active-longpress' : ''}}"
            style="border-left: 1px solid #ddd; padding-left: 10rpx; -webkit-tap-highlight-color: transparent !important;"
            id="reply-{{reply.id}}"
            catchlongpress="showReplyActionSheet"
            data-reply="{{reply}}"
            data-comment-id="{{item.id}}">

        <view class="reply-content"
              bindtap="onReplyClick"
              data-id="{{item.id}}"
              data-username="{{message.is_anonymous_display ? reply.anonymous_name : reply.username}}"
              data-type="reply"
              data-userid="{{message.is_anonymous_display ? reply.original_user_id : reply.user_id}}"
              data-replyid="{{reply.id}}"
              style="-webkit-tap-highlight-color: transparent !important;">
          <view class="touxiang1" style="display: flex; align-items: center;">
            <image src="{{reply.face_url}}" style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
            <text style="margin-left: 15rpx;">{{message.is_anonymous_display ? reply.anonymous_name : reply.username}}</text>
            <view wx:if="{{message.is_anonymous_display && reply.is_original_poster}}" class="cyber-badge">
              <view class="cyber-btn">
                帖主
                <view class="cyber-number">OP</view>
              </view>
            </view>
            <text wx:elif="{{!message.is_anonymous_display && reply.titlename && reply.titlename !== '无' && reply.titlename !== 'undefined'}}" class="touxian2 bg{{reply.titlecolor}}">{{reply.titlename}}</text>
          </view>
          <!-- 回复内容部分 -->
          <view style="line-height: 45rpx; overflow: hidden; word-wrap: break-word; margin-top:15rpx;" class="text">
            <text wx:if="{{reply.reply_type === 'reply'}}" style="color: #808080;">回复 {{message.is_anonymous_display ? reply.reply_to_anonymous_name : reply.reply_to_username}}: </text>
            <text>{{reply.content}}</text>
          </view>
          <!-- 添加回复图片显示 -->
          <view wx:if="{{reply.images && reply.images.length > 0}}" class="image-container" catch:tap="onReplyClick" data-id="{{item.id}}" data-username="{{message.is_anonymous_display ? reply.anonymous_name : reply.username}}" data-type="reply" data-userid="{{message.is_anonymous_display ? reply.original_user_id : reply.user_id}}" data-replyid="{{reply.id}}">
            <image wx:for="{{reply.images}}" wx:key="index" wx:for-item="img"
                   src="{{img}}" mode="aspectFill" class="uniform-image"
                   catchtap="handleReplyImageClick"
                   data-url="{{img}}"
                   data-images="{{reply.images}}">
            </image>
          </view>
        </view>
        <!-- 回复内容下方的时间和点赞区域 -->
        <view class="kuang"
              style="display: flex; justify-content: space-between; align-items: center; height: 50rpx; padding-left: 10rpx; -webkit-tap-highlight-color: transparent !important;">
          <view class="gradient-text" style="line-height: 50rpx; flex: 1; -webkit-tap-highlight-color: transparent !important;" bindtap="onReplyClick" data-id="{{item.id}}" data-username="{{message.is_anonymous_display ? reply.anonymous_name : reply.username}}" data-type="reply" data-userid="{{message.is_anonymous_display ? reply.original_user_id : reply.user_id}}" data-replyid="{{reply.id}}">{{reply.send_timestamp}}</view>
          <view class="last" style="font-size: 25rpx; display: flex; align-items:center;gap: 4.5rpx; -webkit-tap-highlight-color: transparent !important;" catchtap="dolike3" data-replyid="{{reply.id}}" data-commentid="{{item.id}}">
            <image src="{{reply.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" mode="aspectFit" style="width: 35rpx; height: 35rpx; -webkit-tap-highlight-color: transparent !important;" />
            <text style="min-width: 20rpx; text-align: right; -webkit-tap-highlight-color: transparent !important;">{{reply.total_likes || 0}}</text>
          </view>
        </view>
      </view>

      <!-- 展开/收起按钮 -->
      <view wx:if="{{item.replies.length > defaultRepliesShow}}"
            class="expand-replies-btn"
            bindtap="toggleReplies"
            catch:longpress="skipGuide"
            data-id="{{item.id}}">
        <view wx:if="{{!expandedComments[item.id] || expandedComments[item.id] < item.replies.length}}" class="expand-text">
          <text class="expand-icon">↓</text> 展开 {{expandedComments[item.id] ? (item.replies.length - expandedComments[item.id]) : (item.replies.length - defaultRepliesShow)}} 条回复
        </view>
        <view wx:else class="expand-text">
          <text class="expand-icon">↑</text> 收起回复
        </view>
      </view>
    </view>
  </view>

  <!-- 加载中显示 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-dots"></view>
  </view>

  <!-- 无评论时显示 -->
  <view wx:if="{{!isLoading && (!comment || comment.length === 0)}}" class="no-comment">
    <image src="https://www.bjgaoxiaoshequ.store/images/content.png" mode="aspectFit" class="no-comment-image"></image>
    <text class="no-comment-text">一片荒原，来说点什么吧～</text>
  </view>

  <view style="height: 30rpx;"></view>
  <!-- 遮罩层和弹窗 -->
  <block wx:if="{{showModal}}">
    <view class="modal-mask" bindtap="handleModalClose"></view>
    <view class="modal" style="width:70%;">
      <view class="modal-header3">
        <view class="modal-header4">联系方式</view>
      </view>
      <view class="profile-container2" style="--index: 0">
        <view class="label">微信号</view>
        <text class="input-right" user-select="true">{{message.vx}}</text>
      </view>
      <view class="profile-container2" style="--index: 1">
        <view class="label">QQ号</view>
        <text class="input-right" user-select="true">{{message.qq}}</text>
      </view>
      <view class="profile-container2" style="--index: 2">
        <view class="label">手机号</view>
        <text class="input-right" user-select="true">{{message.phonenumber}}</text>
      </view>
      <view class="modal-close" bindtap="handleModalClose">
        <image src="/images/guanbi.png" style="width: 36rpx; height: 36rpx;"></image>
      </view>
    </view>
  </block>

  <!-- 引导提示遮罩层 -->
  <view class="guide-hint-mask {{showGuideHint ? 'guide-show' : 'guide-hide'}} {{guideStep === 2 ? 'step-2' : ''}} {{guideClosing ? 'guide-closing' : ''}}" wx:if="{{showGuideHint || guideClosing}}" catchtouchmove="true">
    <view class="mask-background"></view>
    <view class="guide-content">
      <!-- 第一步：评论区按钮引导 -->
      <view class="guide-tip" wx:if="{{showGuideHint && guideStep === 1}}">
        <view class="guide-tip-text">
          <text>✨ 点击这里可以查看帖子浏览量，点击任意一条评论可以回复ta哦~</text>
        </view>
        <view class="guide-tip-buttons">
          <button class="guide-btn" bindtap="nextGuideStep">下一步</button>
          <button class="guide-btn guide-skip" bindtap="skipGuide">跳过</button>
        </view>
      </view>

      <!-- 第二步：排序框按钮引导 -->
      <view class="guide-tip" wx:if="{{showGuideHint && guideStep === 2}}">
        <view class="guide-tip-text">
          <text>✨ 点击这里可以切换按时间或点赞数排序，连续点击可以切换升/降序~</text>
        </view>
        <view class="guide-tip-buttons">
          <button class="guide-btn" bindtap="closeGuideHint">知道了</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部遮罩层 -->
  <view class="bottom-mask"></view>

  <!-- 自定义ActionSheet -->
  <view class="custom-action-sheet-mask" wx:if="{{showCustomActionSheet}}" bindtap="closeCustomActionSheet"></view>
  <view class="custom-action-sheet" wx:if="{{showCustomActionSheet}}">
    <view class="custom-action-sheet-title">选择操作</view>
    <view class="custom-action-sheet-options">
      <button class="custom-action-option share-option" open-type="share">
        <text>分享给朋友</text>
      </button>
      <view class="custom-action-option delete-option" wx:if="{{canDelete}}" bindtap="handleDelete">
        <text>删除</text>
      </view>
      <view class="custom-action-option report-option" wx:if="{{!canDelete}}" bindtap="handleReport">
        <text>举报</text>
      </view>
    </view>
    <view class="custom-action-cancel" bindtap="closeCustomActionSheet">取消</view>
  </view>

  <!-- 隐藏的分享按钮，但不覆盖整个屏幕 -->
  <button class="hidden-share-button" open-type="share" id="shareBtn" style="position: fixed; top: -999px; left: -999px; width: 1px; height: 1px; opacity: 0;"></button>
  
  <!-- 直接分享按钮，点击三点菜单时自动触发 -->
  <button wx:if="{{showShareBtn}}" class="direct-share-button" open-type="share" id="directShareBtn">分享给朋友</button>
  
  <!-- 分享面板 -->
  <view class="share-panel-mask" wx:if="{{showSharePanel}}" bindtap="closeSharePanel"></view>
  <view class="share-panel" wx:if="{{showSharePanel}}">
    <view class="share-panel-title">分享到</view>
    <view class="share-options">
      <button class="share-option" open-type="share">
        <image src="/images/fenxiang2.png" class="share-icon"></image>
        <text>微信好友</text>
      </button>
      <button class="share-option" open-type="share">
        <image src="/images/pinglun2.png" class="share-icon"></image>
        <text>朋友圈</text>
      </button>
    </view>
    <view class="share-cancel" bindtap="closeSharePanel">取消</view>
  </view>
  
  <!-- 底部固定操作栏 -->
  <view class="bottom-fixed-bar">
    <view class="input-section" bindtap="handleCommentClick">
      <image src="/images/dangshidati-01.png" class="comment-icon" />
      <text class="input-placeholder">说点什么...</text>
    </view>
    <view class="action-section">
      <view class="action-item" bindtap="handleCommentClick" style="-webkit-tap-highlight-color: transparent;">
        <image src="/images/pinglun2.png" class="action-icon" />
        <text class="action-text">{{message.total_pinglun}}</text>
      </view>
      <view class="action-item" bindtap="dolike" style="-webkit-tap-highlight-color: transparent;">
        <image src="{{message.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" class="action-icon" />
        <text class="action-text">{{message.total_likes}}</text>
      </view>
    </view>
  </view>

  <!-- 评论遮罩层 -->
  <view wx:if="{{commentSectionVisible}}" class="comment-mask" bindtap="hideCommentSection"></view>

  <!-- 评论输入框 -->
  <view wx:if="{{commentSectionVisible}}" 
        class="fixed-comment-section {{showEmoji ? 'comment-section-with-emoji' : ''}} {{showEmojiList ? 'comment-section-with-emoji-list' : ''}} {{keyboardHeight > 0 ? 'comment-section-with-keyboard' : ''}}" 
        style="{{keyboardHeight > 0 ? '--keyboard-height:' + keyboardHeight + 'px' : ''}}">
    
    <!-- 图片预览区域 -->
    <view class="preview-area" wx:if="{{tempImages.length > 0}}">
      <view class="preview-images">
        <view class="preview-image-item" wx:for="{{tempImages}}" wx:key="index">
          <image src="{{item}}" mode="aspectFill" class="preview-image" bindtap="previewImages" data-index="{{index}}" />
          <view class="close-btn" bindtap="removeImage" data-index="{{index}}">×</view>
          <view class="preview-text">预览</view>
        </view>
      </view>
    </view>

    <!-- 常用表情栏 - 最上方 -->
    <view class="quick-emoji-bar">
      <view class="quick-emoji-item" wx:for="{{quickEmojiList}}" wx:key="index" bindtap="selectEmoji" data-emoji="{{item}}">
        <text class="emoji-text">{{item}}</text>
      </view>
    </view>

    <!-- 评论输入框 - 中间 -->
    <textarea class="comment-textarea" 
              placeholder="{{commentPlaceholder}}" 
              value="{{content}}" 
              bindinput="onCommentInput" 
              bindblur="handleInputBlur"
              bindfocus="handleInputFocus"
              bindkeyboardheightchange="handleKeyboardHeightChange"
              focus="{{commentInputFocused}}"
              fixed="true"
              show-confirm-bar="{{false}}"
              adjust-position="{{false}}"
              cursor-spacing="20"
              maxlength="100"
              auto-height="{{true}}"
              style="min-height: 80rpx; max-height: 200rpx;"
              hold-keyboard="{{true}}" />

    <!-- 工具栏 - 底部 -->
    <view class="tools-row">
      <view class="left-tools">
        <view class="tool-item" bindtap="chooseImage">
          <image src="/images/tupian.png" class="tool-icon" />
          <view class="image-count" wx:if="{{tempImages.length > 0}}">{{tempImages.length}}</view>
        </view>
        <view class="tool-item" bindtap="openCamera">
          <image src="/images/xiangji.png" class="tool-icon" />
        </view>
        <!-- 隐藏自定义表情栏按钮 -->
        <!-- <view class="tool-item" bindtap="toggleEmoji">
          <image src="/images/biaoqing.png" class="tool-icon" />
        </view> -->
        <view class="tool-item" bindtap="toggleEmojiList">
          <text class="emoji-icon">😊</text>
        </view>
      </view>
      <button class="submit-button" bindtap="submitComment" disabled="{{isSubmitting}}">{{isSubmitting ? '发送中...' : '发送'}}</button>
    </view>
  </view>

  <!-- 表情面板 -->
  <view class="emoji-panel" wx:if="{{showEmoji}}">
    <scroll-view scroll-y class="emoji-scroll">
      <view class="emoji-grid">
        <view class="emoji-item" wx:for="{{emojiConfig.list}}" wx:key="id" bindtap="selectEmoji" data-emoji="{{item}}">
          <image src="{{item.url}}" class="emoji-image" mode="aspectFit" lazy-load />
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- emoji表情面板 -->
  <view class="emoji-list-panel" wx:if="{{showEmojiList}}">
    <scroll-view scroll-y class="emoji-list-scroll">
      <view class="emoji-list-grid">
        <view class="emoji-list-item" wx:for="{{emojiList}}" wx:key="index" bindtap="selectEmojiText" data-emoji="{{item}}">
          <text>{{item}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 页面最底部挂载隐藏的举报组件 -->
  <report id="reportComponent" />
</view>
