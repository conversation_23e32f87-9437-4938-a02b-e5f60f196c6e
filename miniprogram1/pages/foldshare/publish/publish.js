// pages/publish/publish.js
const { navigateBack } = require('../../../utils/navigation');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    qq: '',
    vx: '',
    phonenumber: '',
    contactresult: '未填写',
    showModal: false,
    selectedIndex: '0',
    showModal2: false,
    selectedType: '请选择',
    selected: '',
    selectedItem: {},
    showDropdown: false,
    images: [],
    content1: '',
    jine: '',
    isPublishing: false,
    weizhi: '',
    items: [
      { value: '2', name: '发条说说' },
      { value: '3', name: '校园交易' },
      { value: '4', name: '告白倾诉' },
      { value: '5', name: '拼车交流' },
      { value: '6', name: '失物寻找' },
      { value: '71', name: '租房求助' },
      { value: '72', name: '房屋出租' },
      { value: '8', name: '赚点外快' },
      { value: '99', name: '寻找搭子' }
    ],
    selected: '',
    isVoteEnabled: false,
    voteType: 'single',
    voteTitle: '',  // 添加投票标题
    voteOptions: ['', ''],  // 默认两个空选项
    minOptionsRequired: 2,  // 最少需要的选项数
    maxOptionsAllowed: 5,  // 最多允许的选项数
    isAnonymousEnabled: false,  // 匿名发布开关
    anonymousCategories: ['2', '4', '99'],  // 支持匿名的类目：发条说说、告白倾诉、寻找搭子
  },
  handleModalClose2() {
    this.setData({ showModal2: false });
  },
  handleModalClose() {
    const { vx, qq, phonenumber } = this.data; // 从 data 获取变量值

    // 检查是否所有字段为空
    const allEmpty = !vx && !qq && !phonenumber;

    // 更新 contactresult 和 showModal
    this.setData({
      contactresult: allEmpty ? '未填写' : '已填写✅', // 根据条件设置值
      showModal: false, // 隐藏模态框
    });
  },
  dianji4: function (e) {
    const value = e.currentTarget.dataset.index; // 获取点击的头衔索引
    const name = e.currentTarget.dataset.name; // 获取头衔对象

    // 检查新选择的类目是否支持匿名，如果不支持则关闭匿名开关
    const anonymousCategories = ['2', '4', '99']; // 发条说说、告白倾诉、寻找搭子
    const newIsAnonymousEnabled = anonymousCategories.includes(value) ? this.data.isAnonymousEnabled : false;

    this.setData({
      selectedIndex: value, // 更新选中的索引
      selectedType: name,// 存储选中的touxian_name
      showModal2: !this.data.showModal2,
      isAnonymousEnabled: newIsAnonymousEnabled  // 根据类目支持情况更新匿名状态
    });
  },
  dianji3(e) {
    this.setData({
      showModal2: true,
    });
  },
  dianji5(e) {
    this.setData({
      showModal: true,
    });
  },
  radioChange(e) {
    const selectedValue = e.detail.value;
    const selectedItem = this.data.items.find(item => item.value === selectedValue);

    this.setData({
      selected: selectedValue,
      selectedItem: selectedItem,
      showDropdown: false
    });
  },
  onClickLeft() {
    navigateBack();
  },

  onClickRight() {
    wx.showToast({ title: '点击按钮', icon: 'none' });
  },
  onInputChange(event) {
    this.setData({
      jine: event.detail.value
    });
  },
  onInputChange2(event) {
    this.setData({
      weizhi: event.detail.value
    });
  },
  onInputChange3(event) {
    this.setData({
      vx: event.detail.value
    });
  },
  onInputChange4(event) {
    this.setData({
      qq: event.detail.value
    });
  },
  onInputChange5(event) {
    this.setData({
      phonenumber: event.detail.value
    });
  },
  onChange1(event) {
    this.setData({
      content1: event.detail.value
    });
  },
  previewImage(e) {
    const index = e.currentTarget.dataset.index; // 获取点击的图片索引
    wx.previewImage({
      current: this.data.images[index], // 当前预览的图片
      urls: this.data.images // 图片列表
    });
  },
  chooseImage() {
    const that = this;
    wx.chooseImage({
      count: 9 - this.data.images.length, // 限制选择的图片数量
      sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
      success(res) {
        const tempFilePaths = res.tempFilePaths;
        that.setData({
          images: that.data.images.concat(tempFilePaths) // 将新选择的图片加入数组
        });
      }
    });
  },

  publish() {
    if (this.data.isPublishing) return;
    
    // 验证类别是否选择
    if (this.data.selectedType === '请选择') {
      wx.showToast({
        title: '请选择发布类别',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 验证投票选项
    if (this.data.isVoteEnabled && !this.validateVoteOptions()) {
      return;
    }

    this.setData({ isPublishing: true });
    wx.showLoading({
      title: '正在上传...',
    });

    // 如果有图片，先上传图片
    if (this.data.images.length > 0) {
      const uploadPromises = this.data.images.map(imagePath => {
        return new Promise((resolve, reject) => {
          if (!imagePath) {
            resolve(null);
            return;
          }
          wx.uploadFile({
            url: getApp().globalData.wangz + '/message/uploadImage',
            filePath: imagePath,
            name: 'file',
            formData: {
              'user_id': getApp().globalData.user_id
            },
            success(res) {
              try {
                const data = JSON.parse(res.data);
                if (data.error_code === 0) {
                  if (data.data && data.data.image_url) {
                    resolve(data.data.image_url);
                  } else {
                    resolve(data.image_url); // 兼容直接返回image_url的情况
                  }
                } else {
                  console.error('上传失败:', data);
                  resolve(null);
                }
              } catch (e) {
                console.error('JSON 解析错误:', e.message);
                resolve(null);
              }
            },
            fail(err) {
              console.error('图片上传失败:', err);
              resolve(null);
            }
          });
        });
      });

      Promise.all(uploadPromises)
        .then(uploadedImages => {
          // 过滤掉上传失败的图片（null值）
          const validImages = uploadedImages.filter(img => img !== null);
          this.publishWithImages(validImages);
        })
        .catch(error => {
          wx.hideLoading();
          this.setData({ isPublishing: false });
          wx.showToast({
            title: '图片上传失败',
            icon: 'error',
            duration: 2000
          });
        });
    } else {
      // 没有图片，直接发布
      this.publishWithImages([]);
    }
  },

  publishWithImages(uploadedImages) {
    // 从本地存储获取用户信息
    const userId = wx.getStorageSync('user_id');
    const username = wx.getStorageSync('username');
    const faceUrl = wx.getStorageSync('face_url');
    const titlename = wx.getStorageSync('titlename') || '无';
    const titlecolor = wx.getStorageSync('titlecolor') || '0';

    // 获取投票数据
    const voteData = this.formatVoteData();

    // 确保jine和weizhi是字符串类型
    const jine = typeof this.data.jine === 'string' ? this.data.jine : this.data.jine.toString();
    const weizhi = typeof this.data.weizhi === 'string' ? this.data.weizhi : this.data.weizhi.toString();

    // 确保images是一个有效的数组，过滤掉null值
    const validImages = uploadedImages.filter(img => img !== null && img !== undefined);

    const postData = {
      user_id: userId,
      username: username,
      face_url: faceUrl,
      titlename: titlename,
      titlecolor: titlecolor,
      content: this.data.content1 || '',
      // choose:'199',
      choose: this.data.selectedIndex || '0',
      images: validImages,
      jine: jine || '',
      weizhi: weizhi || '',
      vx: this.data.vx || '',
      qq: this.data.qq || '',
      phonenumber: this.data.phonenumber || '',
      voteData: this.data.isVoteEnabled ? voteData : null,
      is_anonymous: this.data.isAnonymousEnabled ? 1 : 0  // 添加匿名参数
    };

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      // 重置发送状态
      this.setData({ isPublishing: false });
      wx.hideLoading();
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/message/publish',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Accept': 'application/json',
        'token': token
      },
      data: postData,
      success: (res) => {
        wx.hideLoading();
        if (res.data.error_code === 0) {
          wx.showToast({
            title: res.data.msg || '发布成功',
            icon: 'success',
            duration: 1000
          });
          
          // 发布成功后，刷新上一页的数据
          setTimeout(() => {
            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2];
            if (prevPage) {
              // 判断上一页是home页面还是需求页面
              const pagePath = prevPage.route;
              if (pagePath.includes('fold1/home/<USER>')) {
                // home页面
                const selectedItemId = prevPage.data.selectedItemId;
                const pageKey = `page${selectedItemId}`;
                const messagesKey = `messages${selectedItemId}`;
                
                prevPage.setData({
                  [pageKey]: 1,
                  [messagesKey]: []
                });
                prevPage.loadMessages();
              } else if (pagePath.includes('fold2/xuqiu/xuqiu')) {
                // 需求页面
                prevPage.setData({
                  page: 1,
                  list: []
                });
                prevPage.sendRequest();
              }
            }
            navigateBack();
          }, 1000);
        } else {
          wx.showToast({
            title: res.data.msg || '发布失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'none',
          duration: 2000
        });
      },
      complete: () => {
        this.setData({ isPublishing: false });
      }
    });
  },

  formatVoteData() {
    if (!this.data.isVoteEnabled) return null;
    
    const validOptions = this.data.voteOptions.filter(opt => opt.trim());
    if (!this.validateVoteOptions()) return null;
    
    return {
      title: this.data.voteTitle.trim(),  // 使用用户输入的标题，移除前后空格
      type: this.data.voteType,  // 'single' 或 'multiple'
      options: validOptions,
      deadline: null  // 暂时不设置截止时间
    };
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.loadFontFace({
      family: '阿里妈妈刀隶体 Regular发布',
      global: false,
      source: 'url("https://www.bjgaoxiaoshequ.store/阿里妈妈刀隶体/发布.woff2")' //
    });
    
    // 处理来自发布弹窗的类型参数
    if (options.type) {
      let selectedIndex = '0';
      let selectedType = '请选择';
      
      // 根据type参数匹配相应的发布类型
      switch(options.type) {
        case '2':  // 发条说说
          selectedIndex = '2';
          selectedType = '发条说说';
          break;
        case '3':  // 校园交易
          selectedIndex = '3';
          selectedType = '校园交易';
          break;
        case '4':  // 告白倾诉
          selectedIndex = '4';
          selectedType = '告白倾诉';
          break;
        case '5':  // 拼车交流
          selectedIndex = '5';
          selectedType = '拼车交流';
          break;
        case '6':  // 失物寻找
          selectedIndex = '6';
          selectedType = '失物寻找';
          break;
        case '71':  // 租房求助
          selectedIndex = '71';
          selectedType = '租房求助';
          break;
        case '72':  // 房屋出租
          selectedIndex = '72';
          selectedType = '房屋出租';
          break;
        case '8':  // 赚点外快
          selectedIndex = '8';
          selectedType = '赚点外快';
          break;
        case '99':  // 寻找搭子
          selectedIndex = '99';
          selectedType = '寻找搭子';
          break;
      }
      
      this.setData({
        selectedIndex,
        selectedType
      });
    }
  },

  /**
   * 生命周期函数--页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  toggleVote() {
    wx.vibrateShort({ type: 'medium' });  // 添加轻微震动反馈
    this.setData({
      isVoteEnabled: !this.data.isVoteEnabled
    });
  },

  // 切换匿名发布
  toggleAnonymous() {
    // 检查当前选择的类目是否支持匿名
    const anonymousCategories = ['2', '4', '99']; // 发条说说、告白倾诉、寻找搭子
    if (!anonymousCategories.includes(this.data.selectedIndex)) {
      wx.showToast({
        title: '当前类目不支持匿名发布',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.vibrateShort({ type: 'medium' });  // 添加轻微震动反馈
    this.setData({
      isAnonymousEnabled: !this.data.isAnonymousEnabled
    });
  },

  switchVoteType(e) {
    wx.vibrateShort({ type: 'light' });  // 添加轻微震动反馈
    this.setData({
      voteType: e.currentTarget.dataset.type
    });
  },

  handleOptionInput(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;
    const voteOptions = [...this.data.voteOptions];
    voteOptions[index] = value;
    this.setData({ voteOptions });
  },

  addOption() {
    if (this.data.voteOptions.length < this.data.maxOptionsAllowed) {
      wx.vibrateShort({ type: 'light' });
      this.setData({
        voteOptions: [...this.data.voteOptions, '']
      });
    } else {
      wx.showToast({
        title: `最多只能添加${this.data.maxOptionsAllowed}个选项`,
        icon: 'none',
        duration: 2000
      });
    }
  },

  deleteOption(e) {
    wx.vibrateShort({ type: 'medium' });  // 添加中等强度震动反馈
    const { index } = e.currentTarget.dataset;
    const voteOptions = this.data.voteOptions.filter((_, i) => i !== index);
    this.setData({ voteOptions });
  },

  // 验证投票选项
  validateVoteOptions() {
    if (!this.data.isVoteEnabled) return true;
    
    // 验证投票标题
    if (!this.data.voteTitle.trim()) {
      wx.showToast({
        title: '请输入投票标题',
        icon: 'none',
        duration: 2000
      });
      return false;
    }
    
    // 过滤掉空选项
    const validOptions = this.data.voteOptions.filter(opt => opt.trim());
    
    if (validOptions.length < this.data.minOptionsRequired) {
      wx.showToast({
        title: `至少需要${this.data.minOptionsRequired}个投票选项`,
        icon: 'none',
        duration: 2000
      });
      return false;
    }
    
    // 检查是否有重复选项
    const uniqueOptions = new Set(validOptions);
    if (uniqueOptions.size !== validOptions.length) {
      wx.showToast({
        title: '投票选项不能重复',
        icon: 'none',
        duration: 2000
      });
      return false;
    }
    
    return true;
  },

  handleVoteTitleInput(e) {
    this.setData({
      voteTitle: e.detail.value
    });
  }
})